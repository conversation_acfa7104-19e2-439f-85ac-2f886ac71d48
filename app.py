#!/usr/bin/env python3
"""
Flask HTTP server that accepts GET and POST requests and returns a hash of the submitted data.
"""

from flask import Flask, request, jsonify
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import hashlib
import json

app = Flask(__name__)

# Initialize the rate limiter
limiter = Limiter(
    app=app,
    key_func=get_remote_address,  # Rate limit based on IP address
    default_limits=["2 per 5 seconds"]  # Default rate limit: 2 requests per 5 seconds
)

def generate_hash(data):
    """Generate SHA-256 hash of the provided data."""
    if isinstance(data, dict):
        # Convert dict to JSON string for consistent hashing
        data_str = json.dumps(data, sort_keys=True)
    elif isinstance(data, str):
        data_str = data
    else:
        data_str = str(data)
    
    return hashlib.sha256(data_str.encode('utf-8')).hexdigest()

@app.route('/', methods=['GET', 'POST'])
@limiter.limit("2 per 5 seconds")
def hash_data():
    """Handle both GET and POST requests and return hash of the data."""
    
    if request.method == 'GET':
        # For GET requests, hash the query parameters
        data = dict(request.args)
        if not data:
            data = "No query parameters provided"
        
        hash_value = generate_hash(data)
        
        return jsonify({
            'method': 'GET',
            'data': data,
            'hash': hash_value,
            'hash_algorithm': 'SHA-256'
        })
    
    elif request.method == 'POST':
        # For POST requests, try to get JSON data first, then form data, then raw data
        if request.is_json:
            data = request.get_json()
        elif request.form:
            data = dict(request.form)
        else:
            data = request.get_data(as_text=True)
            if not data:
                data = "No data provided"
        
        hash_value = generate_hash(data)
        
        return jsonify({
            'method': 'POST',
            'data': data,
            'hash': hash_value,
            'hash_algorithm': 'SHA-256'
        })

@app.route('/health', methods=['GET'])
@limiter.limit("10 per 5 seconds")  # More lenient for health checks
def health_check():
    """Simple health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'message': 'Flask HTTP server is running'
    })

@app.errorhandler(429)
def ratelimit_handler(e):
    """Handle rate limit exceeded errors."""
    return jsonify({
        'error': 'Rate limit exceeded',
        'message': 'Too many requests. Please wait before making another request.',
        'retry_after': str(e.retry_after) + ' seconds' if hasattr(e, 'retry_after') else 'a few seconds'
    }), 429



if __name__ == '__main__':
    print("Starting Flask HTTP server...")
    # print("Server will be available at: http://localhost:5000")
    # print("Health check endpoint: http://localhost:5000/health")

    app.run(host='0.0.0.0', port=5001, debug=True)
