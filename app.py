#!/usr/bin/env python3
"""
Flask HTTPS server that accepts GET and POST requests and returns a hash of the submitted data.
"""

from flask import Flask, request, jsonify
import hashlib
import json
import ssl
import os

app = Flask(__name__)

def generate_hash(data):
    """Generate SHA-256 hash of the provided data."""
    if isinstance(data, dict):
        # Convert dict to JSON string for consistent hashing
        data_str = json.dumps(data, sort_keys=True)
    elif isinstance(data, str):
        data_str = data
    else:
        data_str = str(data)
    
    return hashlib.sha256(data_str.encode('utf-8')).hexdigest()

@app.route('/', methods=['GET', 'POST'])
def hash_data():
    """Handle both GET and POST requests and return hash of the data."""
    
    if request.method == 'GET':
        # For GET requests, hash the query parameters
        data = dict(request.args)
        if not data:
            data = "No query parameters provided"
        
        hash_value = generate_hash(data)
        
        return jsonify({
            'method': 'GET',
            'data': data,
            'hash': hash_value,
            'hash_algorithm': 'SHA-256'
        })
    
    elif request.method == 'POST':
        # For POST requests, try to get JSON data first, then form data, then raw data
        if request.is_json:
            data = request.get_json()
        elif request.form:
            data = dict(request.form)
        else:
            data = request.get_data(as_text=True)
            if not data:
                data = "No data provided"
        
        hash_value = generate_hash(data)
        
        return jsonify({
            'method': 'POST',
            'data': data,
            'hash': hash_value,
            'hash_algorithm': 'SHA-256'
        })

@app.route('/health', methods=['GET'])
def health_check():
    """Simple health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'message': 'Flask HTTPS server is running'
    })

def create_self_signed_cert():
    """Create a self-signed certificate for HTTPS."""
    from cryptography import x509
    from cryptography.x509.oid import NameOID
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.asymmetric import rsa
    from cryptography.hazmat.primitives import serialization
    import datetime
    
    # Generate private key
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048,
    )
    
    # Create certificate
    subject = issuer = x509.Name([
        x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
        x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "CA"),
        x509.NameAttribute(NameOID.LOCALITY_NAME, "San Francisco"),
        x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Flask HTTPS Server"),
        x509.NameAttribute(NameOID.COMMON_NAME, "localhost"),
    ])
    
    cert = x509.CertificateBuilder().subject_name(
        subject
    ).issuer_name(
        issuer
    ).public_key(
        private_key.public_key()
    ).serial_number(
        x509.random_serial_number()
    ).not_valid_before(
        datetime.datetime.utcnow()
    ).not_valid_after(
        datetime.datetime.utcnow() + datetime.timedelta(days=365)
    ).add_extension(
        x509.SubjectAlternativeName([
            x509.DNSName("localhost"),
            x509.IPAddress("127.0.0.1"),
        ]),
        critical=False,
    ).sign(private_key, hashes.SHA256())
    
    # Write certificate and private key to files
    with open("cert.pem", "wb") as f:
        f.write(cert.public_bytes(serialization.Encoding.PEM))
    
    with open("key.pem", "wb") as f:
        f.write(private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        ))

if __name__ == '__main__':
    # Create SSL certificate if it doesn't exist
    if not os.path.exists('cert.pem') or not os.path.exists('key.pem'):
        print("Creating self-signed SSL certificate...")
        create_self_signed_cert()
        print("SSL certificate created successfully!")
    
    # Create SSL context
    context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
    context.load_cert_chain('cert.pem', 'key.pem')
    
    print("Starting Flask HTTPS server...")
    print("Server will be available at: https://localhost:5000")
    print("Health check endpoint: https://localhost:5000/health")
    print("Note: You may see a security warning due to the self-signed certificate")
    
    app.run(host='0.0.0.0', port=5000, ssl_context=context, debug=True)
