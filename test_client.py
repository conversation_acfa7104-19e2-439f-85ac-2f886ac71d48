#!/usr/bin/env python3
"""
Test client to demonstrate the Flask HTTP server functionality.
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_get_request():
    """Test GET request with query parameters."""
    print("Testing GET request with query parameters...")
    
    params = {
        'name': '<PERSON>',
        'age': '30',
        'city': 'New York'
    }
    
    response = requests.get(BASE_URL, params=params)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print("-" * 50)

def test_post_json():
    """Test POST request with JSON data."""
    print("Testing POST request with JSON data...")
    
    data = {
        'username': 'testuser',
        'email': '<EMAIL>',
        'message': 'Hello, World!'
    }
    
    response = requests.post(BASE_URL, json=data)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print("-" * 50)

def test_post_form():
    """Test POST request with form data."""
    print("Testing POST request with form data...")
    
    data = {
        'field1': 'value1',
        'field2': 'value2',
        'field3': 'value3'
    }
    
    response = requests.post(BASE_URL, data=data)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print("-" * 50)

def test_health_check():
    """Test health check endpoint."""
    print("Testing health check endpoint...")
    
    response = requests.get(f"{BASE_URL}/health")
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print("-" * 50)

if __name__ == '__main__':
    print("Flask HTTPS Server Test Client")
    print("=" * 50)
    
    try:
        test_health_check()
        test_get_request()
        test_post_json()
        test_post_form()
        
        print("All tests completed successfully!")
        
    except requests.exceptions.ConnectionError:
        print("Error: Could not connect to the server.")
        print("Make sure the Flask server is running at http://localhost:5000")
    except Exception as e:
        print(f"Error: {e}")
