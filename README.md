# Flask HTTP Hash Server

A simple Flask HTTP server that accepts GET and POST requests and returns a SHA-256 hash of the submitted data.

## Features

- **Simple HTTP Server**: Lightweight and easy to use
- **Rate Limiting**: Limits requests to 2 per 5-second window per IP address
- **GET Requests**: Hashes query parameters
- **POST Requests**: Supports JSON, form data, and raw text data
- **Health Check**: Simple endpoint to verify server status

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Starting the Server

Run the Flask server:
```bash
python app.py
```

The server will start on `http://localhost:5001`

### API Endpoints

#### Main Endpoint: `/`

**GET Request**
- Hashes query parameters
- Example: `http://localhost:5001?name=John&age=30`
- Rate limited to 2 requests per 5 seconds per IP

**POST Request**
- Supports JSON data, form data, or raw text
- Returns hash of the submitted data
- Rate limited to 2 requests per 5 seconds per IP

#### Health Check: `/health`

**GET Request**
- Simple health check endpoint
- Returns server status
- Rate limited to 10 requests per 5 seconds per IP

### Example Requests

#### Using curl

**GET with query parameters:**
```bash
curl "http://localhost:5001?name=John&age=30"
```

**POST with JSON data:**
```bash
curl -X POST -H "Content-Type: application/json" \
  -d '{"username":"testuser","message":"Hello World"}' \
  http://localhost:5001
```

**POST with form data:**
```bash
curl -X POST -d "field1=value1&field2=value2" \
  http://localhost:5001
```

**Health check:**
```bash
curl http://localhost:5001/health
```

#### Using the Test Client

Run the included test client:
```bash
python test_client.py
```

This will demonstrate all the different types of requests the server can handle.

#### Testing Rate Limiting

Run the rate limiting test script:
```bash
python test_rate_limit.py
```

This script will:
- Test the 2 requests per 5 seconds limit on the main endpoint
- Demonstrate rate limit recovery after the time window
- Test the more lenient rate limiting on the health endpoint

### Response Format

All responses include:
- `method`: The HTTP method used (GET or POST)
- `data`: The data that was hashed
- `hash`: The SHA-256 hash of the data
- `hash_algorithm`: Always "SHA-256"

Example response:
```json
{
  "method": "POST",
  "data": {
    "username": "testuser",
    "message": "Hello World"
  },
  "hash": "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3",
  "hash_algorithm": "SHA-256"
}
```

### Rate Limiting

The server implements rate limiting to prevent abuse:

- **Main endpoint (`/`)**: Limited to 2 requests per 5 seconds per IP address
- **Health endpoint (`/health`)**: Limited to 10 requests per 5 seconds per IP address

When rate limit is exceeded, the server returns a 429 status code with the following response:

```json
{
  "error": "Rate limit exceeded",
  "message": "Too many requests. Please wait before making another request.",
  "retry_after": "X seconds"
}
```

## Files

- `app.py`: Main Flask application with rate limiting
- `requirements.txt`: Python dependencies (Flask and Flask-Limiter)
- `test_client.py`: Test client for demonstration
- `test_rate_limit.py`: Rate limiting test script
- `README.md`: This documentation
