# Flask HTTPS Hash Server

A Flask HTTPS server that accepts GET and POST requests and returns a SHA-256 hash of the submitted data.

## Features

- **HTTPS Support**: Uses self-signed SSL certificates for secure communication
- **GET Requests**: Hashes query parameters
- **POST Requests**: Supports JSON, form data, and raw text data
- **Health Check**: Simple endpoint to verify server status
- **Automatic SSL Certificate Generation**: Creates self-signed certificates if they don't exist

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Starting the Server

Run the Flask server:
```bash
python app.py
```

The server will:
- Generate a self-signed SSL certificate (if not already present)
- Start on `https://localhost:5000`
- Display a security warning in browsers (expected for self-signed certificates)

### API Endpoints

#### Main Endpoint: `/`

**GET Request**
- Hashes query parameters
- Example: `https://localhost:5000?name=John&age=30`

**POST Request**
- Supports JSON data, form data, or raw text
- Returns hash of the submitted data

#### Health Check: `/health`

**GET Request**
- Simple health check endpoint
- Returns server status

### Example Requests

#### Using curl

**GET with query parameters:**
```bash
curl -k "https://localhost:5000?name=John&age=30"
```

**POST with JSON data:**
```bash
curl -k -X POST -H "Content-Type: application/json" \
  -d '{"username":"testuser","message":"Hello World"}' \
  https://localhost:5000
```

**POST with form data:**
```bash
curl -k -X POST -d "field1=value1&field2=value2" \
  https://localhost:5000
```

**Health check:**
```bash
curl -k https://localhost:5000/health
```

#### Using the Test Client

Run the included test client:
```bash
python test_client.py
```

This will demonstrate all the different types of requests the server can handle.

### Response Format

All responses include:
- `method`: The HTTP method used (GET or POST)
- `data`: The data that was hashed
- `hash`: The SHA-256 hash of the data
- `hash_algorithm`: Always "SHA-256"

Example response:
```json
{
  "method": "POST",
  "data": {
    "username": "testuser",
    "message": "Hello World"
  },
  "hash": "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3",
  "hash_algorithm": "SHA-256"
}
```

## Security Notes

- The server uses self-signed SSL certificates, which will trigger security warnings in browsers
- For production use, replace with proper SSL certificates from a trusted CA
- The `-k` flag in curl examples disables certificate verification (only use for testing)

## Files

- `app.py`: Main Flask application
- `requirements.txt`: Python dependencies
- `test_client.py`: Test client for demonstration
- `cert.pem`: SSL certificate (auto-generated)
- `key.pem`: SSL private key (auto-generated)
- `README.md`: This documentation
